{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.10.0", "multer": "^1.4.5-lts.1", "razorpay": "^2.9.5", "socket.io": "^4.8.1", "styled-components": "^6.1.18"}}