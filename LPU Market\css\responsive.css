/* Responsive Design */

/* Tablet Styles */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: #2c3e50;
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        gap: 12px;
        border-top: 2px solid #1DCD9F;
        z-index: 1000;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    .nav-menu.active {
        display: flex;
        opacity: 1;
        transform: translateY(0);
    }

    .nav-menu.active .nav-link,
    .nav-menu.active .dropdown {
        animation: slideInLeft 0.3s ease forwards;
    }

    .nav-menu.active .nav-link:nth-child(1) { animation-delay: 0.1s; }
    .nav-menu.active .nav-link:nth-child(2) { animation-delay: 0.15s; }
    .nav-menu.active .dropdown:nth-child(2) { animation-delay: 0.15s; }
    .nav-menu.active .nav-link:nth-child(3) { animation-delay: 0.2s; }
    .nav-menu.active .nav-link:nth-child(4) { animation-delay: 0.25s; }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .nav-menu .nav-link {
        padding: 14px 20px;
        border-radius: 10px;
        transition: all 0.3s ease;
        text-align: left;
        font-weight: 600;
        font-size: 1.05rem;
        color: #ffffff;
        text-decoration: none;
        position: relative;
        overflow: hidden;
    }

    .nav-menu .nav-link::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 4px;
        height: 100%;
        background: #1DCD9F;
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .nav-menu .nav-link:hover,
    .nav-menu .nav-link.active {
        background: linear-gradient(135deg, rgba(29, 205, 159, 0.2) 0%, rgba(29, 205, 159, 0.1) 100%);
        color: #1DCD9F;
        transform: translateX(8px);
        box-shadow: 0 4px 12px rgba(29, 205, 159, 0.15);
    }

    .nav-menu .nav-link:hover::before,
    .nav-menu .nav-link.active::before {
        transform: scaleY(1);
    }

    /* Enhanced dropdown toggle styling */
    .nav-menu .dropdown-toggle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu-toggle.active {
        background: rgba(29, 205, 159, 0.2);
        border-color: #1DCD9F;
    }

    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-4px, 5px);
        background: #169976;
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
        transform: scale(0);
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-4px, -5px);
        background: #169976;
    }

    /* Mobile dropdown - click only */
    .dropdown {
        position: relative;
    }

    .dropdown-content {
        display: none;
        position: static;
        box-shadow: none;
        background: rgba(0, 0, 0, 0.3);
        margin-top: 8px;
        border-radius: 8px;
        border: 1px solid rgba(29, 205, 159, 0.2);
        padding: 8px 0;
        transition: all 0.3s ease;
    }

    .dropdown.active .dropdown-content {
        display: block;
        animation: slideDown 0.3s ease;
    }

    .dropdown-content a {
        padding: 12px 20px;
        color: #ffffff;
        text-decoration: none;
        display: block;
        transition: all 0.3s ease;
        border-radius: 6px;
        margin: 2px 8px;
    }

    .dropdown-content a:hover {
        background: rgba(29, 205, 159, 0.1);
        color: #1DCD9F;
        transform: translateX(8px);
    }

    .dropdown-toggle::after {
        content: ' ▼';
        font-size: 0.8em;
        transition: transform 0.3s ease;
    }

    .dropdown.active .dropdown-toggle::after {
        transform: rotate(180deg);
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 60px 20px;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-image {
        margin-top: 40px;
    }

    .hero-img {
        max-width: 300px;
        max-height: 300px;
    }

    .hero-placeholder {
        width: 250px;
        height: 250px;
        font-size: 3rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 20px;
    }

    .category-card {
        padding: 30px 15px;
    }

    .category-icon {
        font-size: 2.8rem;
        width: 80px;
        height: 80px;
        padding: 15px;
    }

    .category-card:hover {
        transform: translateY(-8px);
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
    }

    .product-card {
        max-width: 100%;
        margin: 0 auto;
    }

    .product-card:hover {
        transform: translateY(-8px);
    }

    .product-image {
        height: 160px;
        font-size: 2.5rem;
    }

    .product-info {
        padding: 16px;
    }

    .product-title {
        font-size: 1rem;
        line-height: 1.3;
        margin-bottom: 8px;
    }

    .product-price {
        font-size: 1.2rem;
        margin-bottom: 8px;
    }

    .product-seller,
    .product-date {
        font-size: 0.85rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .form-container {
        margin: 80px 20px;
        padding: 30px 20px;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    .nav-container {
        padding: 0 15px;
    }

    .nav-auth {
        gap: 8px;
    }

    .nav-auth .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
        border-radius: 20px;
    }

    .nav-auth .btn-outline {
        border: 2px solid rgba(29, 205, 159, 0.6);
        color: #1DCD9F;
        background: transparent;
    }

    .nav-auth .btn-outline:hover {
        background: rgba(29, 205, 159, 0.1);
        transform: scale(1.05);
    }

    .nav-auth .btn-primary {
        background: linear-gradient(135deg, #1DCD9F 0%, #169976 100%);
        border: 2px solid transparent;
        color: white;
        font-weight: 600;
    }

    .nav-auth .btn-primary:hover {
        background: linear-gradient(135deg, #169976 0%, #0f7a5c 100%);
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(29, 205, 159, 0.3);
    }

    .btn {
        padding: 10px 16px;
        font-size: 14px;
    }

    .btn-large {
        padding: 14px 24px;
        font-size: 15px;
    }

    .hero {
        padding: 40px 15px;
    }

    .hero h1 {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .hero p {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .hero-buttons .btn {
        width: 100%;
    }

    .hero-img {
        max-width: 250px;
        max-height: 250px;
    }

    .hero-placeholder {
        width: 200px;
        height: 200px;
        font-size: 2.5rem;
    }

    .categories {
        padding: 60px 15px;
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .category-card {
        padding: 25px 10px;
    }

    .category-icon {
        font-size: 2.5rem;
        width: 70px;
        height: 70px;
        padding: 12px;
    }

    .category-card h3 {
        font-size: 1.1rem;
    }

    .category-card p {
        font-size: 0.8rem;
    }

    .category-card:hover {
        transform: translateY(-5px);
    }

    .featured-products {
        padding: 60px 15px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 0 5px;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 30px;
    }

    .product-card {
        max-width: 100%;
        border-radius: 12px;
    }

    .product-image {
        height: 140px;
        font-size: 2rem;
    }

    .product-info {
        padding: 14px;
    }

    .product-title {
        font-size: 0.95rem;
        line-height: 1.2;
        margin-bottom: 6px;
    }

    .product-price {
        font-size: 1.1rem;
        margin-bottom: 6px;
    }

    .product-seller {
        font-size: 0.8rem;
        margin-bottom: 4px;
    }

    .product-date {
        font-size: 0.75rem;
    }

    .footer {
        padding: 40px 15px 15px;
    }

    .footer-content {
        gap: 25px;
    }

    .form-container {
        margin: 80px 15px;
        padding: 25px 15px;
    }

    .container {
        padding: 0 15px;
    }
}

/* Large Desktop Styles */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }

    .nav-container {
        max-width: 1400px;
    }

    .hero h1 {
        font-size: 3.5rem;
    }

    .hero-placeholder {
        width: 350px;
        height: 350px;
        font-size: 4.5rem;
    }

    .categories-grid {
        max-width: 1200px;
    }

    .section-title {
        font-size: 3rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .btn,
    .mobile-menu-toggle {
        display: none !important;
    }

    .hero {
        margin-top: 0;
        background: white !important;
        color: black !important;
    }

    .product-card,
    .category-card {
        box-shadow: none !important;
        border: 1px solid #ddd;
    }

    body {
        background: white !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-outline {
        border-width: 3px;
    }

    .product-card,
    .category-card {
        border: 2px solid #333;
    }

    .form-control {
        border-width: 3px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Responsive styles for About and Contact pages */
@media (max-width: 768px) {
    .mission-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .mission-placeholder {
        width: 300px;
        height: 300px;
        font-size: 4rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .steps-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .contact-info-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .user-info {
        flex-direction: column;
        text-align: center;
    }

    .profile-actions {
        justify-content: center;
    }

    .profile-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .tab-buttons {
        flex-direction: column;
    }

    .user-products-grid {
        grid-template-columns: 1fr;
    }

    .product-details-container {
        grid-template-columns: 1fr;
        gap: 30px;
        margin: 80px 20px;
        padding: 30px 20px;
    }

    .product-image-large {
        height: 300px;
    }

    .placeholder-large {
        font-size: 4rem;
    }
}

@media (max-width: 480px) {
    .about-hero-content h1,
    .contact-hero-content h1 {
        font-size: 2rem;
    }

    .mission-text h2 {
        font-size: 2rem;
    }

    .mission-placeholder {
        width: 250px;
        height: 250px;
        font-size: 3rem;
    }

    .mission-point {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .point-icon {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-item .stat-number {
        font-size: 2.5rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .contact-info-cards {
        gap: 15px;
    }

    .contact-info-card {
        padding: 20px;
    }

    .faq-question {
        padding: 15px;
    }

    .faq-answer {
        padding: 15px;
    }

    .profile-stats {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 20px 15px;
    }

    .tab-content {
        padding: 20px 15px;
    }

    .user-product-card .product-actions {
        flex-direction: column;
        gap: 8px;
    }

    .user-product-card .product-actions .btn {
        width: 100%;
    }
}

/* Extra Small Mobile Styles */
@media (max-width: 360px) {
    .products-grid {
        gap: 12px;
        padding: 0 2px;
    }

    .product-card {
        border-radius: 10px;
    }

    .product-image {
        height: 120px;
        font-size: 1.8rem;
    }

    .product-info {
        padding: 12px;
    }

    .product-title {
        font-size: 0.9rem;
        line-height: 1.1;
    }

    .product-price {
        font-size: 1rem;
    }

    .product-seller,
    .product-date {
        font-size: 0.75rem;
    }

    .featured-products {
        padding: 50px 10px;
    }

    .section-title {
        font-size: 1.6rem;
        margin-bottom: 25px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --card-bg: #2d2d2d;
        --border-color: #404040;
        --muted-text: #cccccc;
        --light-bg: #333333;
    }

    body {
        background-color: var(--bg-color) !important;
        color: var(--text-color) !important;
    }

    /* Navigation */
    .navbar,
    .dropdown-content {
        background: var(--card-bg) !important;
        color: var(--text-color) !important;
    }

    .nav-link,
    .nav-logo h2,
    .nav-logo h2 a,
    .user-name {
        color: var(--text-color) !important;
    }

    .dropdown-content a {
        color: var(--text-color) !important;
    }

    .dropdown-content a:hover {
        background-color: var(--light-bg) !important;
    }

    /* Cards and Containers */
    .product-card,
    .category-card,
    .form-container,
    .feature-card,
    .stat-card,
    .contact-info-card,
    .faq-item,
    .profile-header,
    .profile-tabs,
    .step-card,
    .mission-section,
    .features-section,
    .how-it-works,
    .contact-content,
    .filters-section,
    .products-section,
    .quick-categories,
    .demo-credentials,
    .signup-benefits,
    .posting-tips {
        background: var(--card-bg) !important;
        color: var(--text-color) !important;
    }

    /* Text Elements */
    h1, h2, h3, h4, h5, h6,
    .product-title,
    .category-card h3,
    .feature-card h3,
    .step-card h3,
    .mission-text h2,
    .point-content h3,
    .contact-form-section h2,
    .contact-info-section h2,
    .faq-section h3,
    .user-details h1,
    .products-header h3 {
        color: var(--text-color) !important;
    }

    .section-title {
        color: var(--text-color) !important;
        opacity: 1 !important;
    }

    /* Muted Text */
    p, .product-seller, .product-date, .product-category,
    .user-email, .user-joined, .form-text,
    .point-content p, .feature-card p, .step-card p,
    .mission-text > p, .cta-content p,
    .contact-form-section p, .contact-info-section p,
    .contact-info-card p, .faq-answer p,
    .products-count, .stat-label {
        color: var(--muted-text) !important;
    }

    /* Form Controls */
    .form-control,
    .search-input {
        background: var(--light-bg) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color) !important;
    }

    .form-control::placeholder {
        color: #999 !important;
    }

    /* Buttons */
    .btn-primary {
        color: white !important;
    }

    .btn-primary:hover {
        color: white !important;
    }

    .btn-outline {
        color: #1DCD9F !important;
        border-color: #1DCD9F !important;
        background: transparent !important;
    }

    .btn-outline:hover {
        background: linear-gradient(135deg, #1DCD9F 0%, #169976 100%) !important;
        color: white !important;
    }

    .view-btn {
        background: var(--light-bg) !important;
        border-color: var(--border-color) !important;
        color: var(--text-color) !important;
    }

    .view-btn.active {
        background: #1DCD9F !important;
        color: white !important;
    }

    /* Tabs */
    .tab-buttons {
        background: var(--light-bg) !important;
    }

    .tab-btn {
        color: var(--muted-text) !important;
        background: transparent !important;
    }

    .tab-btn.active {
        background: var(--card-bg) !important;
        color: #1DCD9F !important;
    }

    .tab-btn:hover {
        background: rgba(29, 205, 159, 0.2) !important;
    }

    /* FAQ */
    .faq-section h3 {
        color: var(--text-color) !important;
    }

    .faq-item {
        border-color: var(--border-color) !important;
        background: var(--card-bg) !important;
    }

    .faq-item:hover,
    .faq-item.active {
        border-color: #1DCD9F !important;
    }

    .faq-question {
        background: linear-gradient(135deg, var(--light-bg) 0%, var(--card-bg) 100%) !important;
        color: var(--text-color) !important;
    }

    .faq-question:hover {
        background: linear-gradient(135deg, var(--border-color) 0%, var(--light-bg) 100%) !important;
    }

    .faq-item.active .faq-question {
        background: linear-gradient(135deg, rgba(29, 205, 159, 0.2) 0%, var(--light-bg) 100%) !important;
    }

    .faq-question span:first-child {
        color: var(--text-color) !important;
    }

    .faq-toggle {
        color: #1DCD9F !important;
        background: rgba(29, 205, 159, 0.2) !important;
    }

    .faq-item.active .faq-toggle {
        background: #1DCD9F !important;
        color: white !important;
    }

    .faq-answer {
        background: var(--card-bg) !important;
        border-color: var(--border-color) !important;
    }

    .faq-answer p {
        color: var(--muted-text) !important;
    }

    /* Contact Form */
    .contact-form {
        background: var(--light-bg) !important;
    }

    /* Footer */
    .footer {
        background: #111 !important;
        color: var(--text-color) !important;
    }

    .footer-section h3,
    .footer-section h4 {
        color: #1DCD9F !important;
    }

    .footer-section ul li a {
        color: var(--muted-text) !important;
    }

    .footer-bottom {
        color: var(--muted-text) !important;
    }

    /* Special Elements */
    .mission-placeholder {
        background: linear-gradient(135deg, var(--light-bg) 0%, var(--border-color) 100%) !important;
    }

    .hero-placeholder {
        background: rgba(255,255,255,0.1) !important;
    }

    /* Links */
    a {
        color: #1DCD9F !important;
    }

    a:hover {
        color: #169976 !important;
    }

    /* Breadcrumb */
    .breadcrumb {
        color: var(--muted-text) !important;
    }

    /* Product Status */
    .status-active {
        background: rgba(29, 205, 159, 0.2) !important;
        color: #1DCD9F !important;
    }

    .status-sold {
        background: rgba(34, 34, 34, 0.2) !important;
        color: #222222 !important;
    }

    .status-deleted {
        background: rgba(220, 53, 69, 0.2) !important;
        color: #dc3545 !important;
    }

    /* Empty State */
    .empty-state h3 {
        color: var(--text-color) !important;
    }

    .empty-state p {
        color: var(--muted-text) !important;
    }
}
