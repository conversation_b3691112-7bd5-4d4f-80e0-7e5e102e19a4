<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - LPU Market</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><a href="../index.html" style="text-decoration: none; color: inherit;">LPU Market</a></h2>
            </div>
            
            <div class="nav-menu">
                <a href="../index.html" class="nav-link">Home</a>
                <div class="dropdown">
                    <a href="#" class="nav-link dropdown-toggle">Categories</a>
                    <div class="dropdown-content">
                        <a href="category.html?cat=electronics">Electronics</a>
                        <a href="category.html?cat=books">Books</a>
                        <a href="category.html?cat=hostel">Hostel Essentials</a>
                        <a href="category.html?cat=clothing">Clothing</a>
                        <a href="category.html?cat=misc">Miscellaneous</a>
                    </div>
                </div>
                <a href="about.html" class="nav-link">About</a>
                <a href="contact.html" class="nav-link">Contact</a>
            </div>
            
            <div class="nav-auth" id="navAuth">
                <a href="login.html" class="btn btn-outline">Login</a>
                <a href="signup.html" class="btn btn-primary">Sign Up</a>
            </div>
            
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Profile Content -->
    <main class="main-content">
        <div class="container" style="margin-top: 100px;">
            <!-- Profile Header -->
            <div class="profile-header">
                <div id="userInfo" class="user-info">
                    <!-- User info will be loaded here -->
                </div>
                <div class="profile-actions">
                    <a href="post-product.html" class="btn btn-primary">📤 Post New Product</a>
                    <button class="btn btn-outline" onclick="editProfile()">✏️ Edit Profile</button>
                    <button class="btn btn-danger" onclick="logout()">🚪 Logout</button>
                </div>
            </div>

            <!-- Profile Stats -->
            <div class="profile-stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalProducts">0</div>
                    <div class="stat-label">Products Posted</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeProducts">0</div>
                    <div class="stat-label">Active Listings</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalViews">0</div>
                    <div class="stat-label">Total Views</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="memberDays">0</div>
                    <div class="stat-label">Days as Member</div>
                </div>
            </div>

            <!-- Product Management Tabs -->
            <div class="profile-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="showTab('active')">Active Products</button>
                    <button class="tab-btn" onclick="showTab('sold')">Sold Products</button>
                    <button class="tab-btn" onclick="showTab('deleted')">Deleted Products</button>
                </div>
                
                <div class="tab-content">
                    <div id="activeTab" class="tab-pane active">
                        <div class="products-header">
                            <h3>Your Active Products</h3>
                            <div class="products-actions">
                                <select id="sortProducts" class="form-control">
                                    <option value="newest">Newest First</option>
                                    <option value="oldest">Oldest First</option>
                                    <option value="price-high">Price: High to Low</option>
                                    <option value="price-low">Price: Low to High</option>
                                    <option value="title">Title A-Z</option>
                                </select>
                            </div>
                        </div>
                        <div id="userProducts" class="user-products-grid">
                            <!-- User products will be loaded here -->
                        </div>
                    </div>
                    
                    <div id="soldTab" class="tab-pane">
                        <div class="empty-state">
                            <h3>No sold products yet</h3>
                            <p>Products marked as sold will appear here.</p>
                        </div>
                    </div>
                    
                    <div id="deletedTab" class="tab-pane">
                        <div id="deletedProducts" class="user-products-grid">
                            <!-- Deleted products will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>LPU Market</h3>
                    <p>Your campus marketplace for buying and selling used items among LPU students.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="post-product.html">Post Product</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="category.html?cat=electronics">Electronics</a></li>
                        <li><a href="category.html?cat=books">Books</a></li>
                        <li><a href="category.html?cat=hostel">Hostel Essentials</a></li>
                        <li><a href="category.html?cat=clothing">Clothing</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LPU Market. Made with ❤️ for LPU students.</p>
            </div>
        </div>
    </footer>

    <script src="../js/utils.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        let userProducts = [];
        
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is logged in
            if (!Auth.isLoggedIn()) {
                Utils.Notification.error('Please login to view your profile');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1000);
                return;
            }
            
            loadProfile();
            setupEventListeners();
        });
        
        function loadProfile() {
            const currentUser = Auth.getCurrentUser();
            userProducts = Products.getProductsBySeller(currentUser.id);
            
            displayUserInfo(currentUser);
            displayProfileStats(currentUser, userProducts);
            displayUserProducts();
        }
        
        function displayUserInfo(user) {
            const userInfo = document.getElementById('userInfo');
            userInfo.innerHTML = `
                <div class="user-avatar">
                    <div class="avatar-placeholder">
                        ${user.name.charAt(0).toUpperCase()}
                    </div>
                </div>
                <div class="user-details">
                    <h1>${user.name}</h1>
                    <p class="user-email">${user.email}</p>
                    <p class="user-joined">Member since ${Utils.DateHelper.format(user.createdAt)}</p>
                </div>
            `;
        }
        
        function displayProfileStats(user, products) {
            const activeProducts = products.filter(p => p.status === 'active');
            const memberDays = Math.floor((new Date() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24));
            
            document.getElementById('totalProducts').textContent = products.length;
            document.getElementById('activeProducts').textContent = activeProducts.length;
            document.getElementById('totalViews').textContent = Math.floor(Math.random() * 500) + 50; // Simulated
            document.getElementById('memberDays').textContent = memberDays;
        }
        
        function displayUserProducts() {
            const activeProducts = userProducts.filter(p => p.status === 'active');
            const deletedProducts = userProducts.filter(p => p.status === 'deleted');
            
            displayProductsInContainer(activeProducts, 'userProducts');
            displayProductsInContainer(deletedProducts, 'deletedProducts');
        }
        
        function displayProductsInContainer(products, containerId) {
            const container = document.getElementById(containerId);
            
            if (products.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>No products found</h3>
                        <p>${containerId === 'userProducts' ? 
                            'Start selling by posting your first product!' : 
                            'Deleted products will appear here.'}</p>
                        ${containerId === 'userProducts' ? 
                            '<a href="post-product.html" class="btn btn-primary">Post Product</a>' : ''}
                    </div>
                `;
                return;
            }
            
            container.innerHTML = '';
            products.forEach(product => {
                const card = createUserProductCard(product);
                container.appendChild(card);
            });
        }
        
        function createUserProductCard(product) {
            const card = Utils.DOM.create('div', 'product-card user-product-card');
            
            card.innerHTML = `
                <div class="product-image">
                    ${product.image ? 
                        `<img src="${product.image}" alt="${product.title}">` :
                        `<span>${Utils.ImageHelper.getPlaceholder(product.category)}</span>`
                    }
                </div>
                <div class="product-info">
                    <h3 class="product-title">${product.title}</h3>
                    <div class="product-price">${Utils.NumberHelper.formatPrice(product.price)}</div>
                    <div class="product-category">${Products.getCategoryDisplayName(product.category)}</div>
                    <div class="product-date">Posted ${Utils.DateHelper.timeAgo(product.createdAt)}</div>
                    <div class="product-status status-${product.status}">
                        Status: ${product.status.charAt(0).toUpperCase() + product.status.slice(1)}
                    </div>
                    <div class="product-actions">
                        <button class="btn btn-sm btn-outline" onclick="viewProduct('${product.id}')">👁️ View</button>
                        ${product.status === 'active' ? `
                            <button class="btn btn-sm btn-outline" onclick="editProduct('${product.id}')">✏️ Edit</button>
                            <button class="btn btn-sm btn-outline" onclick="markAsSold('${product.id}')">✅ Mark Sold</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteProduct('${product.id}')">🗑️ Delete</button>
                        ` : product.status === 'deleted' ? `
                            <button class="btn btn-sm btn-outline" onclick="restoreProduct('${product.id}')">↩️ Restore</button>
                        ` : ''}
                    </div>
                </div>
            `;
            
            return card;
        }
        
        function setupEventListeners() {
            const sortSelect = document.getElementById('sortProducts');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    sortUserProducts(this.value);
                    displayUserProducts();
                });
            }
        }
        
        function sortUserProducts(sortBy) {
            switch (sortBy) {
                case 'newest':
                    userProducts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                    break;
                case 'oldest':
                    userProducts.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
                    break;
                case 'price-low':
                    userProducts.sort((a, b) => a.price - b.price);
                    break;
                case 'price-high':
                    userProducts.sort((a, b) => b.price - a.price);
                    break;
                case 'title':
                    userProducts.sort((a, b) => a.title.localeCompare(b.title));
                    break;
            }
        }
        
        function showTab(tabName) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
            
            // Add active class to selected tab
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }
        
        function viewProduct(productId) {
            window.location.href = `product-details.html?id=${productId}`;
        }
        
        function editProduct(productId) {
            // In a real app, this would open an edit form
            Utils.Notification.info('Edit functionality would open a form to modify the product details');
        }
        
        function markAsSold(productId) {
            if (confirm('Mark this product as sold? This will remove it from active listings.')) {
                // In a real app, you would update the product status
                Utils.Notification.success('Product marked as sold!');
                // Reload profile to reflect changes
                setTimeout(() => {
                    loadProfile();
                }, 1000);
            }
        }
        
        function deleteProduct(productId) {
            if (confirm('Are you sure you want to delete this product? You can restore it later.')) {
                const result = Products.deleteProduct(productId);
                if (result.success) {
                    Utils.Notification.success(result.message);
                    loadProfile(); // Reload to reflect changes
                } else {
                    Utils.Notification.error(result.message);
                }
            }
        }
        
        function restoreProduct(productId) {
            if (confirm('Restore this product to active listings?')) {
                const result = Products.updateProduct(productId, { status: 'active' });
                if (result.success) {
                    Utils.Notification.success('Product restored successfully!');
                    loadProfile(); // Reload to reflect changes
                } else {
                    Utils.Notification.error(result.message);
                }
            }
        }
        
        function editProfile() {
            Utils.Notification.info('Profile editing functionality would be implemented here');
        }
        
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                Auth.logout();
                Utils.Notification.success('Logged out successfully!');
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1000);
            }
        }
    </script>
</body>
</html>
