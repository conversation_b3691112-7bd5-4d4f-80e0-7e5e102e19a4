/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f9f9;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation Styles */
.navbar {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: #1DCD9F;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #1DCD9F;
}

.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border-radius: 8px;
    padding: 10px 0;
    z-index: 1001;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    display: block;
    padding: 10px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown-content a:hover {
    background-color: #f8f9fa;
    color: #1DCD9F;
}

.nav-auth {
    display: flex;
    gap: 15px;
    align-items: center;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-name {
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}



/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: inherit;
}

.btn-primary {
    background: linear-gradient(135deg, #1DCD9F 0%, #169976 100%);
    color: white !important;
    border: none;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #169976 0%, #0f7a5c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(29, 205, 159, 0.3);
    color: white !important;
}

.btn-outline {
    background: transparent;
    color: #1DCD9F;
    border: 2px solid #1DCD9F;
}

.btn-outline:hover {
    background: linear-gradient(135deg, #1DCD9F 0%, #169976 100%);
    color: white !important;
    border-color: transparent;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
}

/* Hero Section */
.hero {
    margin-top: 70px;
    padding: 80px 20px;
    background: linear-gradient(135deg, #222222 0%, #1DCD9F 50%, #169976 100%);
    color: white;
    display: flex;
    align-items: center;
    min-height: 500px;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-img {
    max-width: 500px;
    max-height: 400px;
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.hero-img:hover {
    transform: scale(1.05);
}

.hero-placeholder {
    width: 400px;
    height: 400px;
    background: rgba(255,255,255,0.1);
    /* border-radius: 20px; */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    backdrop-filter: blur(10px);
}

/* Categories Section */
.categories {
    padding: 80px 20px;
    background: white;
}

h2.section-title,
.section-title {
    text-align: center !important;
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 50px !important;
    color: #000000 !important;
    opacity: 1 !important;
    display: block !important;
    visibility: visible !important;
}

/* Specific targeting for the section titles */
.categories .section-title,
.featured-products .section-title {
    color: #000000 !important;
    font-weight: 700 !important;
    font-size: 2.5rem !important;
    background: none !important;
    text-shadow: none !important;
    text-decoration: none !important;
    border: none !important;
    outline: none !important;
    margin: 0 0 50px 0 !important;
    padding: 0 !important;
}

/* White section titles for dark backgrounds */
.how-it-works .section-title,
.stats-section .section-title,
.features-section .section-title {
    color: #ffffff !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.category-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 40px 20px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1DCD9F 0%, #169976 100%);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(29, 205, 159, 0.25);
    border-color: #1DCD9F;
    background: linear-gradient(135deg, #ffffff 0%, #f0fffe 100%);
}

.category-icon {
    font-size: 3.5rem;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #1DCD9F 0%, #169976 100%);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(29, 205, 159, 0.3);
}

.category-card:hover .category-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 30px rgba(29, 205, 159, 0.4);
}

.category-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #222222 !important;
    transition: color 0.3s ease;
}

.category-card:hover h3 {
    color: #1DCD9F !important;
}

.category-card p {
    color: #666 !important;
    font-size: 0.95rem;
    line-height: 1.5;
    transition: color 0.3s ease;
}

.category-card:hover p {
    color: #555 !important;
}

/* Featured Products Section */
.featured-products {
    padding: 80px 20px;
    background: #f8f9fa;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.product-image {
    width: 100%;
    height: 200px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #ccc;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #222222 !important;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1DCD9F !important;
    margin-bottom: 10px;
}

.product-seller {
    font-size: 0.9rem;
    color: #666 !important;
    margin-bottom: 15px;
}

.text-center {
    text-align: center;
}

/* Footer */
.footer {
    background: #222222;
    color: white;
    padding: 50px 20px 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 20px;
    color: #1DCD9F;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #1DCD9F;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Form Styles */
.form-container {
    max-width: 500px;
    margin: 100px auto;
    padding: 40px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: #1DCD9F;
}

.form-control.error {
    border-color: #dc3545;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.success-message {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Category Page Styles */
.category-header {
    background: linear-gradient(135deg, #222222 0%, #1DCD9F 50%, #169976 100%);
    color: white;
    padding: 120px 20px 60px;
    margin-top: 70px;
}

.category-header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.filters-section {
    background: white;
    padding: 30px 20px;
    border-bottom: 1px solid #e1e5e9;
}

.filters-container {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-form {
    display: flex;
    gap: 10px;
    flex: 1;
    min-width: 300px;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
}

.view-toggle {
    display: flex;
    gap: 5px;
}

.view-btn {
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
}

.view-btn.active {
    background: #1DCD9F;
    color: white;
    border-color: #1DCD9F;
}

.products-section {
    padding: 40px 20px;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.products-count {
    color: #666;
    font-weight: 500;
}

.products-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.products-list .product-card {
    display: flex;
    align-items: center;
    padding: 20px;
}

.products-list .product-image {
    width: 120px;
    height: 120px;
    margin-right: 20px;
    flex-shrink: 0;
}

.products-list .product-info {
    flex: 1;
}

.quick-categories {
    background: #f8f9fa;
    padding: 60px 20px;
}

.quick-categories h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.quick-categories-grid {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.quick-category {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    min-width: 100px;
}

.quick-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(29, 205, 159, 0.2);
    color: #1DCD9F;
}

.quick-category .category-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.load-more-container {
    text-align: center;
    margin-top: 40px;
}

/* Form Enhancements */
.form-header {
    text-align: center;
    margin-bottom: 30px;
}

.form-header h1 {
    color: #333;
    margin-bottom: 10px;
}

.form-header p {
    color: #666;
}

.btn-full {
    width: 100%;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
}

.form-text {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.demo-credentials,
.signup-benefits {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #1DCD9F;
}

.demo-credentials h4,
.signup-benefits h4 {
    color: #1DCD9F;
    margin-bottom: 15px;
}

.signup-benefits ul {
    list-style: none;
    padding: 0;
}

.signup-benefits li {
    margin-bottom: 8px;
    color: #333;
}

/* Product Details */
.product-details-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    max-width: 1000px;
    margin: 100px auto;
    padding: 40px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.product-image-large {
    width: 100%;
    height: 400px;
    background: #f0f0f0;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-large {
    font-size: 6rem;
    color: #ccc;
}

.product-info-detailed h1 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #333;
}

.product-price-large {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1DCD9F;
    margin-bottom: 20px;
}

.product-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e1e5e9;
}

.product-meta span {
    color: #666;
    font-size: 14px;
}

.product-description,
.seller-info {
    margin-bottom: 30px;
}

.product-description h3,
.seller-info h3 {
    margin-bottom: 15px;
    color: #333;
}

.product-actions {
    margin-top: 30px;
}

/* User Product Cards */
.user-product-card .product-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Profile Page Styles */
.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 700;
}

.user-details h1 {
    margin-bottom: 5px;
    color: #333;
}

.user-email {
    color: #666;
    margin-bottom: 5px;
}

.user-joined {
    color: #999;
    font-size: 0.9rem;
}

.profile-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 30px 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1DCD9F;
    margin-bottom: 10px;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.profile-tabs {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.tab-btn {
    flex: 1;
    padding: 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: white;
    color: #1DCD9F;
    border-bottom: 3px solid #1DCD9F;
}

.tab-btn:hover {
    background: rgba(29, 205, 159, 0.1);
}

.tab-content {
    padding: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.products-header h3 {
    color: #333;
    margin: 0;
}

.products-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.user-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.product-category {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.product-date {
    color: #999;
    font-size: 0.85rem;
    margin-bottom: 10px;
}

.product-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 15px;
    display: inline-block;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-sold {
    background: #cce7ff;
    color: #004085;
}

.status-deleted {
    background: #f8d7da;
    color: #721c24;
}

/* Breadcrumb */
.breadcrumb {
    margin-bottom: 20px;
    color: #666;
}

.breadcrumb a {
    color: #1DCD9F;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Image Preview */
.image-preview {
    margin-top: 15px;
}

.preview-container {
    position: relative;
    display: inline-block;
}

.remove-image {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 12px;
}

.posting-tips {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #1DCD9F;
}

.posting-tips h4 {
    color: #1DCD9F;
    margin-bottom: 15px;
}

.posting-tips ul {
    list-style: none;
    padding: 0;
}

.posting-tips li {
    margin-bottom: 8px;
    color: #333;
}

/* About Page Styles */
.about-hero,
.contact-hero {
    background: linear-gradient(135deg, #222222 0%, #1DCD9F 50%, #169976 100%);
    color: white;
    padding: 120px 20px 60px;
    margin-top: 70px;
    text-align: center;
}

.about-hero-content h1,
.contact-hero-content h1 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.about-hero-content p,
.contact-hero-content p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.mission-section {
    padding: 80px 20px;
    background: white;
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.mission-text h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #333;
}

.mission-text > p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.8;
}

.mission-points {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.mission-point {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.point-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.point-content h3 {
    margin-bottom: 10px;
    color: #333;
}

.point-content p {
    color: #666;
    line-height: 1.6;
}

.mission-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.mission-placeholder {
    width: 400px;
    height: 400px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 5rem;
    color: #ccc;
}

.features-section {
    padding: 80px 20px;
    background: #f8f9fa;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.feature-card h3 {
    margin-bottom: 15px;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

.stats-section {
    padding: 80px 20px;
    background: linear-gradient(135deg, #222222 0%, #1DCD9F 50%, #169976 100%);
    color: white;
    text-align: center;
}

.stats-section .section-title {
    color: white;
    margin-bottom: 50px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    max-width: 800px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
}

.stat-item .stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #fff;
}

.stat-item .stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

.how-it-works {
    padding: 80px 20px;
    background: white;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.step-card {
    text-align: center;
    padding: 30px 20px;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1DCD9F 0%, #169976 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 20px;
}

.step-card h3 {
    margin-bottom: 15px;
    color: #333;
}

.step-card p {
    color: #666;
    line-height: 1.6;
}

.cta-section {
    padding: 80px 20px;
    background: #f8f9fa;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #000000 !important;
    font-weight: 700;
}

.cta-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Contact Page Styles */
.contact-content {
    padding: 80px 20px;
    background: white;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-form-section h2,
.contact-info-section h2 {
    margin-bottom: 20px;
    color: #333;
}

.contact-form-section p,
.contact-info-section p {
    color: #666;
    margin-bottom: 30px;
}

.contact-form {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 15px;
}

.contact-info-cards {
    display: grid;
    gap: 20px;
    margin-bottom: 40px;
}

.contact-info-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.contact-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1DCD9F 0%, #169976 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-info-card h3 {
    margin-bottom: 5px;
    color: #333;
}

.contact-info-card p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

/* FAQ Styles */
.faq-section {
    margin-top: 40px;
}

.faq-section h3 {
    margin-bottom: 25px;
    color: #000000 !important;
    font-weight: 600;
    font-size: 1.5rem;
}

.faq-item {
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.faq-item:hover {
    border-color: #1DCD9F;
    box-shadow: 0 4px 15px rgba(29, 205, 159, 0.1);
}

.faq-item.active {
    border-color: #1DCD9F;
    box-shadow: 0 4px 15px rgba(29, 205, 159, 0.15);
}

.faq-question {
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: linear-gradient(135deg, #f0fffe 0%, #f8f9fa 100%);
}

.faq-item.active .faq-question {
    background: linear-gradient(135deg, #f0fffe 0%, #e6fffe 100%);
}

.faq-question span:first-child {
    font-weight: 600;
    color: #000000 !important;
    font-size: 1rem;
    line-height: 1.4;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1DCD9F !important;
    transition: all 0.3s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(29, 205, 159, 0.1);
}

.faq-item.active .faq-toggle {
    background: #1DCD9F;
    color: white !important;
    transform: rotate(45deg);
}

.faq-answer {
    padding: 25px;
    background: white;
    display: none;
    border-top: 1px solid #e1e5e9;
}

.faq-answer p {
    margin: 0;
    color: #555 !important;
    line-height: 1.7;
    font-size: 0.95rem;
}

/* Utility Classes */
.mt-20 { margin-top: 20px; }
.mb-20 { margin-bottom: 20px; }
.text-center { text-align: center; }

/* Loading and Empty States */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}
