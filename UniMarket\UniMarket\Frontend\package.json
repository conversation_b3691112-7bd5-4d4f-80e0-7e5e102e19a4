{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "react-scripts start --host 0.0.0.0", "dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.4", "axios": "^1.7.9", "gsap": "^3.12.7", "lucide-react": "^0.475.0", "razorpay": "^2.9.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.4.0", "react-router-dom": "^7.1.5", "react-simple-chatbot": "^0.6.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.3", "react-type-animation": "^3.2.0", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.18", "swiper": "^11.2.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^6.1.0"}}