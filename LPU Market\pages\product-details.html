<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - LPU Market</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><a href="../index.html" style="text-decoration: none; color: inherit;">LPU Market</a></h2>
            </div>
            
            <div class="nav-menu">
                <a href="../index.html" class="nav-link">Home</a>
                <div class="dropdown">
                    <a href="#" class="nav-link dropdown-toggle">Categories</a>
                    <div class="dropdown-content">
                        <a href="category.html?cat=electronics">Electronics</a>
                        <a href="category.html?cat=books">Books</a>
                        <a href="category.html?cat=hostel">Hostel Essentials</a>
                        <a href="category.html?cat=clothing">Clothing</a>
                        <a href="category.html?cat=misc">Miscellaneous</a>
                    </div>
                </div>
                <a href="about.html" class="nav-link">About</a>
                <a href="contact.html" class="nav-link">Contact</a>
            </div>
            
            <div class="nav-auth" id="navAuth">
                <a href="login.html" class="btn btn-outline">Login</a>
                <a href="signup.html" class="btn btn-primary">Sign Up</a>
            </div>
            
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Product Details -->
    <main class="main-content">
        <div id="productDetails">
            <!-- Product details will be loaded here -->
        </div>
        
        <!-- Related Products -->
        <section class="related-products">
            <div class="container">
                <h2 class="section-title">Related Products</h2>
                <div class="products-grid" id="relatedProducts">
                    <!-- Related products will be loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>LPU Market</h3>
                    <p>Your campus marketplace for buying and selling used items among LPU students.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="post-product.html">Post Product</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="category.html?cat=electronics">Electronics</a></li>
                        <li><a href="category.html?cat=books">Books</a></li>
                        <li><a href="category.html?cat=hostel">Hostel Essentials</a></li>
                        <li><a href="category.html?cat=clothing">Clothing</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LPU Market. Made with ❤️ for LPU students.</p>
            </div>
        </div>
    </footer>

    <script src="../js/utils.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadProductDetails();
            loadRelatedProducts();
        });
        
        function loadProductDetails() {
            const productId = Utils.URL.getParam('id');
            if (!productId) {
                showProductNotFound();
                return;
            }

            const product = Products.getProductById(productId);
            if (!product || product.status !== 'active') {
                showProductNotFound();
                return;
            }

            displayProductDetails(product);
            updatePageTitle(product.title);
        }
        
        function showProductNotFound() {
            const container = document.getElementById('productDetails');
            container.innerHTML = `
                <div class="container">
                    <div class="empty-state" style="margin: 100px auto;">
                        <h2>Product Not Found</h2>
                        <p>The product you're looking for doesn't exist or has been removed.</p>
                        <a href="category.html" class="btn btn-primary">Browse All Products</a>
                    </div>
                </div>
            `;
        }
        
        function displayProductDetails(product) {
            const container = document.getElementById('productDetails');
            
            container.innerHTML = `
                <div class="container">
                    <div class="breadcrumb">
                        <a href="../index.html">Home</a> > 
                        <a href="category.html?cat=${product.category}">${Products.getCategoryDisplayName(product.category)}</a> > 
                        ${product.title}
                    </div>
                    
                    <div class="product-details-container">
                        <div class="product-image-large">
                            ${product.image ? 
                                `<img src="${product.image}" alt="${product.title}">` :
                                `<div class="placeholder-large">${Utils.ImageHelper.getPlaceholder(product.category)}</div>`
                            }
                        </div>
                        
                        <div class="product-info-detailed">
                            <h1>${product.title}</h1>
                            <div class="product-price-large">${Utils.NumberHelper.formatPrice(product.price)}</div>
                            
                            <div class="product-meta">
                                <span class="category">
                                    <strong>Category:</strong> ${Products.getCategoryDisplayName(product.category)}
                                </span>
                                <span class="date">
                                    <strong>Posted:</strong> ${Utils.DateHelper.timeAgo(product.createdAt)}
                                </span>
                            </div>
                            
                            <div class="product-description">
                                <h3>Description</h3>
                                <p>${product.description}</p>
                            </div>
                            
                            <div class="seller-info">
                                <h3>Seller Information</h3>
                                <p><strong>Name:</strong> ${product.sellerName}</p>
                                <p><strong>Member since:</strong> ${Utils.DateHelper.format(product.createdAt)}</p>
                                
                                <div class="contact-seller">
                                    ${Auth.isLoggedIn() ? 
                                        `<button class="btn btn-primary" onclick="contactSeller('${product.sellerId}', '${product.title}')">
                                            📞 Contact Seller
                                        </button>` :
                                        `<p class="login-prompt">
                                            <a href="login.html" class="btn btn-primary">Login to Contact Seller</a>
                                        </p>`
                                    }
                                </div>
                            </div>
                            
                            <div class="product-actions">
                                <button class="btn btn-outline" onclick="history.back()">
                                    ← Back to Listings
                                </button>
                                <button class="btn btn-outline" onclick="shareProduct('${product.id}')">
                                    📤 Share Product
                                </button>
                                ${Auth.isLoggedIn() && Auth.getCurrentUser().id === product.sellerId ? 
                                    `<button class="btn btn-danger" onclick="deleteProduct('${product.id}')">
                                        🗑️ Delete Product
                                    </button>` : ''
                                }
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function loadRelatedProducts() {
            const productId = Utils.URL.getParam('id');
            const product = Products.getProductById(productId);
            
            if (!product) return;
            
            // Get products from the same category, excluding current product
            const relatedProducts = Products.getProductsByCategory(product.category)
                .filter(p => p.id !== productId)
                .slice(0, 4);
            
            const container = document.getElementById('relatedProducts');
            
            if (relatedProducts.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <p>No related products found in this category.</p>
                        <a href="category.html?cat=${product.category}" class="btn btn-outline">
                            Browse ${Products.getCategoryDisplayName(product.category)}
                        </a>
                    </div>
                `;
                return;
            }
            
            ProductUI.renderProductsGrid(relatedProducts, container);
        }
        
        function updatePageTitle(productTitle) {
            document.title = `${productTitle} - LPU Market`;
        }
        
        function contactSeller(sellerId, productTitle) {
            if (!Auth.isLoggedIn()) {
                Utils.Notification.error('Please login to contact the seller');
                return;
            }
            
            const currentUser = Auth.getCurrentUser();
            if (currentUser.id === sellerId) {
                Utils.Notification.info('This is your own product!');
                return;
            }
            
            // In a real application, this would open a messaging interface
            const message = `Hi! I'm interested in your product "${productTitle}". Is it still available?`;
            
            // Simulate contact functionality
            Utils.Notification.success('Contact request sent! The seller will get back to you soon.');
            
            // You could implement:
            // - Email integration
            // - In-app messaging
            // - WhatsApp integration
            // - Phone number reveal
        }
        
        function shareProduct(productId) {
            const product = Products.getProductById(productId);
            if (!product) return;
            
            const shareData = {
                title: product.title,
                text: `Check out this ${product.title} for ${Utils.NumberHelper.formatPrice(product.price)} on LPU Market!`,
                url: window.location.href
            };
            
            if (navigator.share) {
                navigator.share(shareData)
                    .then(() => Utils.Notification.success('Product shared successfully!'))
                    .catch(() => fallbackShare(shareData));
            } else {
                fallbackShare(shareData);
            }
        }
        
        function fallbackShare(shareData) {
            // Copy URL to clipboard
            navigator.clipboard.writeText(shareData.url)
                .then(() => {
                    Utils.Notification.success('Product URL copied to clipboard!');
                })
                .catch(() => {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = shareData.url;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    Utils.Notification.success('Product URL copied to clipboard!');
                });
        }
        
        // Override the global deleteProduct function for this page
        window.deleteProduct = function(productId) {
            if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
                const result = Products.deleteProduct(productId);
                if (result.success) {
                    Utils.Notification.success(result.message);
                    setTimeout(() => {
                        window.location.href = 'profile.html';
                    }, 1000);
                } else {
                    Utils.Notification.error(result.message);
                }
            }
        };
    </script>
</body>
</html>
