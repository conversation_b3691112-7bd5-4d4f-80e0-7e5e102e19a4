<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - LPU Market</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><a href="../index.html" style="text-decoration: none; color: inherit;">LPU Market</a></h2>
            </div>
            
            <div class="nav-menu">
                <a href="../index.html" class="nav-link">Home</a>
                <div class="dropdown">
                    <a href="#" class="nav-link dropdown-toggle">Categories</a>
                    <div class="dropdown-content">
                        <a href="category.html?cat=electronics">Electronics</a>
                        <a href="category.html?cat=books">Books</a>
                        <a href="category.html?cat=hostel">Hostel Essentials</a>
                        <a href="category.html?cat=clothing">Clothing</a>
                        <a href="category.html?cat=misc">Miscellaneous</a>
                    </div>
                </div>
                <a href="about.html" class="nav-link">About</a>
                <a href="contact.html" class="nav-link active">Contact</a>
            </div>
            
            <div class="nav-auth" id="navAuth">
                <a href="login.html" class="btn btn-outline">Login</a>
                <a href="signup.html" class="btn btn-primary">Sign Up</a>
            </div>
            
            <div class="mobile-menu-toggle" id="mobileMenuToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Contact Hero Section -->
    <section class="contact-hero">
        <div class="container">
            <div class="contact-hero-content">
                <h1>Get in Touch</h1>
                <p>Have questions, suggestions, or need help? We're here to assist you!</p>
            </div>
        </div>
    </section>

    <!-- Contact Content -->
    <section class="contact-content">
        <div class="container">
            <div class="contact-grid">
                <!-- Contact Form -->
                <div class="contact-form-section">
                    <h2>Send us a Message</h2>
                    <p>Fill out the form below and we'll get back to you as soon as possible.</p>
                    
                    <form id="contactForm" class="contact-form">
                        <div class="form-group">
                            <label for="contactName">Your Name *</label>
                            <input 
                                type="text" 
                                id="contactName" 
                                name="name" 
                                class="form-control" 
                                placeholder="Enter your full name"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="contactEmail">Email Address *</label>
                            <input 
                                type="email" 
                                id="contactEmail" 
                                name="email" 
                                class="form-control" 
                                placeholder="Enter your email address"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label for="contactSubject">Subject *</label>
                            <select id="contactSubject" name="subject" class="form-control" required>
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="technical">Technical Support</option>
                                <option value="account">Account Issues</option>
                                <option value="report">Report a Problem</option>
                                <option value="suggestion">Suggestion/Feedback</option>
                                <option value="partnership">Partnership Inquiry</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="contactMessage">Message *</label>
                            <textarea 
                                id="contactMessage" 
                                name="message" 
                                class="form-control" 
                                rows="6"
                                placeholder="Please describe your inquiry in detail..."
                                required
                                minlength="10"
                            ></textarea>
                            <small class="form-text">Minimum 10 characters required</small>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="newsletter" id="newsletter">
                                <span class="checkmark"></span>
                                Subscribe to our newsletter for updates and announcements
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-full">
                            📧 Send Message
                        </button>
                    </form>
                </div>
                
                <!-- Contact Information -->
                <div class="contact-info-section">
                    <h2>Contact Information</h2>
                    
                    <div class="contact-info-cards">
                        <div class="contact-info-card">
                            <div class="contact-icon">📧</div>
                            <h3>Email Us</h3>
                            <p><EMAIL></p>
                            <p><EMAIL></p>
                        </div>
                        
                        <div class="contact-info-card">
                            <div class="contact-icon">📱</div>
                            <h3>Call Us</h3>
                            <p>+91 98765 43210</p>
                            <p>Mon-Fri: 9:00 AM - 6:00 PM</p>
                        </div>
                        
                        <div class="contact-info-card">
                            <div class="contact-icon">📍</div>
                            <h3>Visit Us</h3>
                            <p>Lovely Professional University</p>
                            <p>Phagwara, Punjab 144411</p>
                        </div>
                        
                        <div class="contact-info-card">
                            <div class="contact-icon">💬</div>
                            <h3>Live Chat</h3>
                            <p>Available on our website</p>
                            <p>Mon-Fri: 10:00 AM - 5:00 PM</p>
                        </div>
                    </div>
                    
                    <!-- FAQ Section -->
                    <div class="faq-section">
                        <h3>Frequently Asked Questions</h3>
                        
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>How do I create an account?</span>
                                <span class="faq-toggle">+</span>
                            </div>
                            <div class="faq-answer">
                                <p>Click on "Sign Up" in the top navigation, fill out the form with your LPU email address, and verify your account through the email we send you.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>Is LPU Market free to use?</span>
                                <span class="faq-toggle">+</span>
                            </div>
                            <div class="faq-answer">
                                <p>Yes! LPU Market is completely free. There are no listing fees, transaction fees, or membership charges.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>How do I contact a seller?</span>
                                <span class="faq-toggle">+</span>
                            </div>
                            <div class="faq-answer">
                                <p>Click on any product to view its details, then click the "Contact Seller" button. You'll need to be logged in to contact sellers.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>What if I have issues with a transaction?</span>
                                <span class="faq-toggle">+</span>
                            </div>
                            <div class="faq-answer">
                                <p>Contact us immediately through this form or email <NAME_EMAIL>. We'll help mediate and resolve any issues.</p>
                            </div>
                        </div>
                        
                        <div class="faq-item">
                            <div class="faq-question" onclick="toggleFAQ(this)">
                                <span>Can I edit or delete my listings?</span>
                                <span class="faq-toggle">+</span>
                            </div>
                            <div class="faq-answer">
                                <p>Yes! Go to your profile page where you can edit, delete, or mark your products as sold.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>LPU Market</h3>
                    <p>Your campus marketplace for buying and selling used items among LPU students.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="post-product.html">Post Product</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="category.html?cat=electronics">Electronics</a></li>
                        <li><a href="category.html?cat=books">Books</a></li>
                        <li><a href="category.html?cat=hostel">Hostel Essentials</a></li>
                        <li><a href="category.html?cat=clothing">Clothing</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 LPU Market. Made with ❤️ for LPU students.</p>
            </div>
        </div>
    </footer>

    <script src="../js/utils.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        function toggleFAQ(element) {
            const faqItem = element.parentNode;
            const answer = faqItem.querySelector('.faq-answer');
            const toggle = element.querySelector('.faq-toggle');
            
            // Close all other FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                if (item !== faqItem) {
                    item.classList.remove('active');
                    item.querySelector('.faq-answer').style.display = 'none';
                    item.querySelector('.faq-toggle').textContent = '+';
                }
            });
            
            // Toggle current FAQ item
            if (faqItem.classList.contains('active')) {
                faqItem.classList.remove('active');
                answer.style.display = 'none';
                toggle.textContent = '+';
            } else {
                faqItem.classList.add('active');
                answer.style.display = 'block';
                toggle.textContent = '−';
            }
        }
        
        // Auto-fill form if user is logged in
        document.addEventListener('DOMContentLoaded', function() {
            const currentUser = Auth.getCurrentUser();
            if (currentUser) {
                document.getElementById('contactName').value = currentUser.name;
                document.getElementById('contactEmail').value = currentUser.email;
            }
        });
    </script>
</body>
</html>
