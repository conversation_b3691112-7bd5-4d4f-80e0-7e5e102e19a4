@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Baloo+2:wght@400..800&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
    background-color: #454545;
}

/* App.css */
.inter-regular {
    font-family: "Inter", sans-serif;
    /* Use sans-serif instead of serif */
    font-optical-sizing: auto;
    font-weight: 400;
    /* Regular weight */
    font-style: normal;
}

.baloo-text {
    font-family: "Baloo 2", sans-serif;
    font-weight: 800; /* Adjust weight as needed */
    font-style: normal;
}

.inter-bold {
    font-family: "Inter", sans-serif;
    font-optical-sizing: auto;
    font-weight: 700;
    /* Bold weight */
    font-style: normal;
}


/* Hide scrollbar for all elements */
::-webkit-scrollbar {
    display: none;
}

/* For Firefox */
html {
    scrollbar-width: none;
}


.swiper-slide {
    opacity: 0.3;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.swiper-slide-active {
    opacity: 1;
    transform: scale(1.4);
}
